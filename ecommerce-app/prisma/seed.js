const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Hash password for demo users
  const hashedPassword = await bcrypt.hash('password123', 12);

  // Create sample users
  const users = await Promise.all([
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: '<PERSON>',
        lastName: '<PERSON><PERSON>'
      }
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: '<PERSON>',
        lastName: '<PERSON>'
      }
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Bob',
        lastName: 'Johnson'
      }
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Admin',
        lastName: 'User'
      }
    })
  ]);

  console.log(`✅ Created ${users.length} users`);

  // Create sample products
  const products = await Promise.all([
    prisma.product.create({
      data: {
        itemName: 'MacBook Pro 16"',
        description: 'Powerful laptop with M3 Pro chip, perfect for professionals',
        tags: ['electronics', 'laptop', 'apple', 'computer'],
        price: 249900, // $2499.00 in cents
        category: 'Electronics',
        stock: 15
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'iPhone 15 Pro',
        description: 'Latest iPhone with titanium design and advanced camera system',
        tags: ['electronics', 'phone', 'apple', 'smartphone'],
        price: 99900, // $999.00 in cents
        category: 'Electronics',
        stock: 50
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'AirPods Pro',
        description: 'Wireless earbuds with active noise cancellation',
        tags: ['electronics', 'audio', 'apple', 'wireless'],
        price: 24900, // $249.00 in cents
        category: 'Electronics',
        stock: 100
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'Samsung Galaxy S24',
        description: 'Android flagship with AI-powered features',
        tags: ['electronics', 'phone', 'samsung', 'smartphone'],
        price: 79900, // $799.00 in cents
        category: 'Electronics',
        stock: 30
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'Dell XPS 13',
        description: 'Ultra-portable laptop with stunning display',
        tags: ['electronics', 'laptop', 'dell', 'computer'],
        price: 129900, // $1299.00 in cents
        category: 'Electronics',
        stock: 20
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'Sony WH-1000XM5',
        description: 'Premium noise-canceling headphones',
        tags: ['electronics', 'audio', 'sony', 'headphones'],
        price: 39900, // $399.00 in cents
        category: 'Electronics',
        stock: 25
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'iPad Air',
        description: 'Versatile tablet for work and creativity',
        tags: ['electronics', 'tablet', 'apple'],
        price: 59900, // $599.00 in cents
        category: 'Electronics',
        stock: 40
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'Nintendo Switch',
        description: 'Hybrid gaming console for home and portable play',
        tags: ['electronics', 'gaming', 'nintendo', 'console'],
        price: 29900, // $299.00 in cents
        category: 'Gaming',
        stock: 35
      }
    })
  ]);

  console.log(`✅ Created ${products.length} products`);

  // Create sample cart items
  const cartItems = await Promise.all([
    prisma.cartItem.create({
      data: {
        userId: users[0].id,
        productId: products[0].id, // MacBook Pro
        quantity: 1
      }
    }),
    prisma.cartItem.create({
      data: {
        userId: users[0].id,
        productId: products[2].id, // AirPods Pro
        quantity: 2
      }
    }),
    prisma.cartItem.create({
      data: {
        userId: users[1].id,
        productId: products[1].id, // iPhone 15 Pro
        quantity: 1
      }
    }),
    prisma.cartItem.create({
      data: {
        userId: users[1].id,
        productId: products[5].id, // Sony Headphones
        quantity: 1
      }
    }),
    prisma.cartItem.create({
      data: {
        userId: users[2].id,
        productId: products[4].id, // Dell XPS 13
        quantity: 1
      }
    }),
    prisma.cartItem.create({
      data: {
        userId: users[2].id,
        productId: products[7].id, // Nintendo Switch
        quantity: 1
      }
    })
  ]);

  console.log(`✅ Created ${cartItems.length} cart items`);

  // Create a sample order
  await prisma.order.create({
    data: {
      cartItemId: cartItems[0].id
    }
  });

  // Mark the cart item as ordered
  await prisma.cartItem.update({
    where: { id: cartItems[0].id },
    data: { isOrdered: true }
  });

  console.log(`✅ Created 1 sample order`);

  console.log('🎉 Database seeded successfully!');
  console.log('\n📊 Summary:');
  console.log(`   Users: ${users.length} (password: "password123" for all)`);
  console.log(`   Products: ${products.length}`);
  console.log(`   Cart Items: ${cartItems.length}`);
  console.log(`   Orders: 1`);
  console.log('\n🔑 Demo Login Credentials:');
  console.log('   Email: <EMAIL>');
  console.log('   Email: <EMAIL>');
  console.log('   Email: <EMAIL>');
  console.log('   Email: <EMAIL>');
  console.log('   Password: password123 (for all users)');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });