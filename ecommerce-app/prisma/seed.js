const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create sample users
  const users = await Promise.all([
    prisma.user.create({
      data: {
        name: '<PERSON>',
        address: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zip: '10001',
          country: 'USA'
        },
        phoneNumber: '******-0123',
        email: '<EMAIL>'
      }
    }),
    prisma.user.create({
      data: {
        name: '<PERSON>',
        address: {
          street: '456 Oak Ave',
          city: 'Los Angeles',
          state: 'CA',
          zip: '90210',
          country: 'USA'
        },
        phoneNumber: '******-0456',
        email: '<EMAIL>'
      }
    }),
    prisma.user.create({
      data: {
        name: '<PERSON>',
        address: {
          street: '789 Pine Rd',
          city: 'Chicago',
          state: 'IL',
          zip: '60601',
          country: 'USA'
        },
        phoneNumber: '******-0789',
        email: '<EMAIL>'
      }
    })
  ]);

  console.log(`✅ Created ${users.length} users`);

  // Create sample products
  const products = await Promise.all([
    prisma.product.create({
      data: {
        itemName: 'MacBook Pro 16"',
        tags: ['electronics', 'laptop', 'apple', 'computer'],
        price: 249900 // $2499.00 in cents
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'iPhone 15 Pro',
        tags: ['electronics', 'phone', 'apple', 'smartphone'],
        price: 99900 // $999.00 in cents
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'AirPods Pro',
        tags: ['electronics', 'audio', 'apple', 'wireless'],
        price: 24900 // $249.00 in cents
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'Samsung Galaxy S24',
        tags: ['electronics', 'phone', 'samsung', 'smartphone'],
        price: 79900 // $799.00 in cents
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'Dell XPS 13',
        tags: ['electronics', 'laptop', 'dell', 'computer'],
        price: 129900 // $1299.00 in cents
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'Sony WH-1000XM5',
        tags: ['electronics', 'audio', 'sony', 'headphones'],
        price: 39900 // $399.00 in cents
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'iPad Air',
        tags: ['electronics', 'tablet', 'apple'],
        price: 59900 // $599.00 in cents
      }
    }),
    prisma.product.create({
      data: {
        itemName: 'Nintendo Switch',
        tags: ['electronics', 'gaming', 'nintendo', 'console'],
        price: 29900 // $299.00 in cents
      }
    })
  ]);

  console.log(`✅ Created ${products.length} products`);

  // Create sample cart items
  const cartItems = await Promise.all([
    prisma.cartItem.create({
      data: {
        userId: users[0].id,
        productId: products[0].id, // MacBook Pro
        quantity: 1
      }
    }),
    prisma.cartItem.create({
      data: {
        userId: users[0].id,
        productId: products[2].id, // AirPods Pro
        quantity: 2
      }
    }),
    prisma.cartItem.create({
      data: {
        userId: users[1].id,
        productId: products[1].id, // iPhone 15 Pro
        quantity: 1
      }
    }),
    prisma.cartItem.create({
      data: {
        userId: users[1].id,
        productId: products[5].id, // Sony Headphones
        quantity: 1
      }
    }),
    prisma.cartItem.create({
      data: {
        userId: users[2].id,
        productId: products[4].id, // Dell XPS 13
        quantity: 1
      }
    }),
    prisma.cartItem.create({
      data: {
        userId: users[2].id,
        productId: products[7].id, // Nintendo Switch
        quantity: 1
      }
    })
  ]);

  console.log(`✅ Created ${cartItems.length} cart items`);

  // Create a sample order
  const order = await prisma.order.create({
    data: {
      cartItemId: cartItems[0].id
    }
  });

  // Mark the cart item as ordered
  await prisma.cartItem.update({
    where: { id: cartItems[0].id },
    data: { isOrdered: true }
  });

  console.log(`✅ Created 1 sample order`);

  console.log('🎉 Database seeded successfully!');
  console.log('\n📊 Summary:');
  console.log(`   Users: ${users.length}`);
  console.log(`   Products: ${products.length}`);
  console.log(`   Cart Items: ${cartItems.length}`);
  console.log(`   Orders: 1`);
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });