generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           Int        @id @default(autoincrement())
  name         String
  address      Json
  phoneNumber  String
  email        String?
  cartItems    CartItem[]
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
}

model Product {
  id        Int        @id @default(autoincrement())
  itemName  String
  tags      String[]
  price     Int
  cartItems CartItem[]
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
}

model CartItem {
  id         Int      @id @default(autoincrement())
  user       User     @relation(fields: [userId], references: [id])
  userId     Int
  product    Product  @relation(fields: [productId], references: [id])
  productId  Int
  quantity   Int
  isOrdered  Boolean  @default(false)
  order      Order?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

model Order {
  id           Int      @id @default(autoincrement())
  cartItem     CartItem @relation(fields: [cartItemId], references: [id])
  cartItemId   Int      @unique
  orderedAt    DateTime @default(now())
  notified     Boolean  @default(false)
}