# E-commerce API v1 Documentation

## Base URL
```
http://localhost:3000/api/v1
```

## Related Documentation
- **[Database Schema](database.md)** - Complete database documentation with ER diagrams
- **[Product Data Flow](product-data-flow.md)** - Product operations and data flow visualizations
- **[README.md](../README.md)** - Project overview and setup guide

## Authentication
Most endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Response Format
All responses follow this format:
```json
{
  "success": true|false,
  "data": {...},
  "message": "Optional message",
  "error": "Error message if success is false"
}
```

## Rate Limiting
- General endpoints: 100 requests per 15 minutes
- Auth endpoints: 5 requests per 15 minutes

---

## Authentication Endpoints

### POST /auth/register
Register a new user.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "<PERSON>",
  "lastName": "Doe"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "createdAt": "2024-01-01T00:00:00.000Z"
    },
    "accessToken": "eyJ...",
    "refreshToken": "eyJ..."
  },
  "message": "User registered successfully"
}
```

### POST /auth/login
User login.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe"
    },
    "accessToken": "eyJ...",
    "refreshToken": "eyJ..."
  },
  "message": "Login successful"
}
```

### POST /auth/refresh
Refresh access token.

**Request:**
```json
{
  "refreshToken": "eyJ..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJ...",
    "refreshToken": "eyJ..."
  }
}
```

### POST /auth/logout
User logout.

**Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Logout successful"
}
```

---

## User Endpoints

### GET /users/profile
Get current user profile. **Requires authentication.**

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### PUT /users/profile
Update current user profile. **Requires authentication.**

**Request:**
```json
{
  "firstName": "Jane",
  "lastName": "Smith"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "Jane",
    "lastName": "Smith",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Profile updated successfully"
}
```

---

## Product Endpoints

### GET /products
Get all products with pagination and filtering.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `tag` (optional): Filter by tag
- `category` (optional): Filter by category
- `minPrice` (optional): Minimum price filter
- `maxPrice` (optional): Maximum price filter

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "itemName": "Smartphone",
      "price": 59999,
      "tags": ["electronics", "mobile"],
      "category": "Electronics",
      "stock": 50,
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

### GET /products/:id
Get product by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "itemName": "Smartphone",
    "price": 59999,
    "tags": ["electronics", "mobile"],
    "description": "Latest smartphone with advanced features",
    "category": "Electronics",
    "stock": 50,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### GET /products/search
Search products.

**Query Parameters:**
- `q` (required): Search query
- `page` (optional): Page number
- `limit` (optional): Items per page

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "itemName": "Smartphone",
      "price": 59999,
      "tags": ["electronics", "mobile"],
      "category": "Electronics",
      "stock": 50
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 5,
    "totalPages": 1
  }
}
```

### GET /products/category/:category
Get products by category.

**Response:** Same as GET /products

---

## Cart Endpoints
**All cart endpoints require authentication.**

### GET /cart
Get user's cart.

**Response:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "quantity": 2,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "product": {
          "id": 1,
          "itemName": "Smartphone",
          "price": 59999,
          "stock": 50
        }
      }
    ],
    "summary": {
      "total": 119998,
      "itemCount": 2,
      "uniqueItems": 1
    }
  }
}
```

### POST /cart
Add item to cart.

**Request:**
```json
{
  "productId": 1,
  "quantity": 2
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "quantity": 2,
    "product": {
      "id": 1,
      "itemName": "Smartphone",
      "price": 59999
    }
  },
  "message": "Item added to cart successfully"
}
```

### PUT /cart/:itemId
Update cart item quantity.

**Request:**
```json
{
  "quantity": 3
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "quantity": 3,
    "product": {
      "id": 1,
      "itemName": "Smartphone",
      "price": 59999
    }
  },
  "message": "Cart item updated successfully"
}
```

### DELETE /cart/:itemId
Remove item from cart.

**Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Item removed from cart successfully"
}
```

### DELETE /cart
Clear entire cart.

**Response:**
```json
{
  "success": true,
  "data": {
    "deletedCount": 3
  },
  "message": "Cart cleared successfully"
}
```

---

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "error": "Validation error message"
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "error": "Invalid or expired token"
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": "Resource not found"
}
```

### 429 Too Many Requests
```json
{
  "success": false,
  "error": "Too many requests, please try again later"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "error": "Internal server error"
}
```
