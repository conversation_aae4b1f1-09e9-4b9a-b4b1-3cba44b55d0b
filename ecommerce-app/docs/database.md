# Database Documentation

This document provides comprehensive documentation for the e-commerce API database schema, including visual diagrams, relationships, and design decisions.

## Table of Contents
- [Database Schema Overview](#database-schema-overview)
- [Entity Relationship Diagram](#entity-relationship-diagram)
- [Table Specifications](#table-specifications)
- [Relationships and Constraints](#relationships-and-constraints)
- [Data Flow Patterns](#data-flow-patterns)
- [Query Patterns](#query-patterns)

## Database Schema Overview

The e-commerce API uses PostgreSQL as the primary database with Prisma ORM for type-safe database operations. The schema is designed to support:

- User authentication and profile management
- Product catalog with categorization and inventory
- Shopping cart functionality
- Order processing and tracking

### Core Entities

```mermaid
erDiagram
    USER {
        int id PK "Auto-increment primary key"
        string email UK "Unique email address"
        string password "Hashed password (bcrypt)"
        string firstName "User's first name"
        string lastName "User's last name"
        datetime createdAt "Account creation timestamp"
        datetime updatedAt "Last profile update"
    }
    
    PRODUCT {
        int id PK "Auto-increment primary key"
        string itemName "Product display name"
        string description "Detailed product description"
        int price "Price in cents (USD)"
        string category "Product category"
        string[] tags "Searchable tags array"
        int stock "Available inventory count"
        datetime createdAt "Product creation timestamp"
        datetime updatedAt "Last product update"
    }
    
    CARTITEM {
        int id PK "Auto-increment primary key"
        int userId FK "Reference to User"
        int productId FK "Reference to Product"
        int quantity "Number of items"
        boolean isOrdered "Order status flag"
        datetime createdAt "Item added to cart"
        datetime updatedAt "Last quantity update"
    }
    
    ORDER {
        int id PK "Auto-increment primary key"
        int cartItemId FK "Reference to CartItem"
        boolean notified "Notification sent flag"
        datetime orderedAt "Order placement timestamp"
    }
    
    USER ||--o{ CARTITEM : "owns"
    PRODUCT ||--o{ CARTITEM : "contains"
    CARTITEM ||--o| ORDER : "becomes"
```

## Entity Relationship Diagram

### Detailed Relationships

```mermaid
graph TD
    A[User Entity] --> B[Authentication Data]
    A --> C[Profile Data]
    A --> D[Cart Relationship]
    
    B --> B1[Email - Unique Index]
    B --> B2[Password - Hashed]
    
    C --> C1[First Name]
    C --> C2[Last Name]
    C --> C3[Timestamps]
    
    D --> E[CartItem Entity]
    E --> F[Product Reference]
    E --> G[Quantity Management]
    E --> H[Order Status]
    
    F --> I[Product Entity]
    I --> J[Basic Info]
    I --> K[Pricing]
    I --> L[Inventory]
    I --> M[Categorization]
    
    J --> J1[ID - Primary Key]
    J --> J2[Item Name]
    J --> J3[Description]
    
    K --> K1[Price in Cents]
    K --> K2[Currency: USD]
    
    L --> L1[Stock Count]
    L --> L2[Availability Logic]
    
    M --> M1[Category String]
    M --> M2[Tags Array]
    
    E --> N[Order Entity]
    N --> O[Order Tracking]
    N --> P[Notification Status]
    
    style A fill:#e3f2fd
    style I fill:#f3e5f5
    style E fill:#e8f5e8
    style N fill:#fff3e0
```

## Table Specifications

### User Table
- **Purpose**: Store user authentication and profile information
- **Primary Key**: `id` (auto-increment)
- **Unique Constraints**: `email`
- **Indexes**: Email for fast authentication lookups

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | Unique user identifier |
| email | VARCHAR(255) | UNIQUE, NOT NULL | User's email address |
| password | VARCHAR(255) | NOT NULL | Bcrypt hashed password |
| firstName | VARCHAR(100) | NOT NULL | User's first name |
| lastName | VARCHAR(100) | NOT NULL | User's last name |
| createdAt | TIMESTAMP | DEFAULT NOW() | Account creation time |
| updatedAt | TIMESTAMP | DEFAULT NOW() | Last update time |

### Product Table
- **Purpose**: Store product catalog information
- **Primary Key**: `id` (auto-increment)
- **Indexes**: Category and tags for search optimization

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | Unique product identifier |
| itemName | VARCHAR(255) | NOT NULL | Product display name |
| description | TEXT | NULL | Detailed product description |
| price | INTEGER | NOT NULL, CHECK > 0 | Price in cents (USD) |
| category | VARCHAR(100) | NULL | Product category |
| tags | TEXT[] | NULL | Array of searchable tags |
| stock | INTEGER | DEFAULT 0, CHECK >= 0 | Available inventory |
| createdAt | TIMESTAMP | DEFAULT NOW() | Product creation time |
| updatedAt | TIMESTAMP | DEFAULT NOW() | Last update time |

### CartItem Table
- **Purpose**: Link users to products with quantities
- **Primary Key**: `id` (auto-increment)
- **Foreign Keys**: `userId` → User.id, `productId` → Product.id
- **Unique Constraint**: (userId, productId, isOrdered) for active cart items

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | Unique cart item identifier |
| userId | INTEGER | FOREIGN KEY, NOT NULL | Reference to User |
| productId | INTEGER | FOREIGN KEY, NOT NULL | Reference to Product |
| quantity | INTEGER | NOT NULL, CHECK > 0 | Number of items |
| isOrdered | BOOLEAN | DEFAULT FALSE | Order status flag |
| createdAt | TIMESTAMP | DEFAULT NOW() | Item added time |
| updatedAt | TIMESTAMP | DEFAULT NOW() | Last update time |

### Order Table
- **Purpose**: Track order status and notifications
- **Primary Key**: `id` (auto-increment)
- **Foreign Key**: `cartItemId` → CartItem.id

| Field | Type | Constraints | Description |
|-------|------|-------------|-------------|
| id | INTEGER | PRIMARY KEY, AUTO_INCREMENT | Unique order identifier |
| cartItemId | INTEGER | FOREIGN KEY, NOT NULL | Reference to CartItem |
| notified | BOOLEAN | DEFAULT FALSE | Notification sent flag |
| orderedAt | TIMESTAMP | DEFAULT NOW() | Order placement time |

## Relationships and Constraints

### Foreign Key Relationships

```mermaid
graph LR
    A[User] -->|1:N| B[CartItem]
    C[Product] -->|1:N| B
    B -->|1:1| D[Order]

    A1[User.id] -.-> B1[CartItem.userId]
    C1[Product.id] -.-> B2[CartItem.productId]
    B3[CartItem.id] -.-> D1[Order.cartItemId]

    style A fill:#e3f2fd
    style C fill:#f3e5f5
    style B fill:#e8f5e8
    style D fill:#fff3e0
```

### Business Logic Constraints

1. **User Constraints**:
   - Email must be unique across all users
   - Password must be hashed before storage
   - Names cannot be empty

2. **Product Constraints**:
   - Price must be positive (> 0)
   - Stock cannot be negative (>= 0)
   - Item name is required

3. **CartItem Constraints**:
   - Quantity must be positive (> 0)
   - User can have only one active cart item per product
   - Cannot add items when stock is insufficient

4. **Order Constraints**:
   - Orders are immutable once created
   - CartItem.isOrdered flag prevents duplicate orders

## Data Flow Patterns

### User Registration and Authentication Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant A as Auth Controller
    participant DB as Database
    participant H as Hash Service

    C->>A: POST /auth/register
    A->>H: Hash password
    H-->>A: Hashed password
    A->>DB: Create user record
    DB-->>A: User created
    A->>A: Generate JWT tokens
    A-->>C: User data + tokens
```

### Product Search and Filtering Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant P as Product Controller
    participant DB as Database

    C->>P: GET /products?category=Electronics&tag=apple
    P->>P: Build filter criteria
    P->>DB: Query with filters + pagination
    DB-->>P: Product results + count
    P->>P: Format response
    P-->>C: Paginated product list
```

### Cart Management Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant Cart as Cart Controller
    participant DB as Database

    C->>Cart: POST /cart {productId, quantity}
    Cart->>DB: Check product stock
    DB-->>Cart: Stock available
    Cart->>DB: Check existing cart item
    alt Item exists
        Cart->>DB: Update quantity
    else New item
        Cart->>DB: Create cart item
    end
    DB-->>Cart: Cart item updated
    Cart-->>C: Success response
```

## Query Patterns

### Common Query Patterns Used in the API

#### 1. Product Listing with Filters
```sql
-- Generated by Prisma for GET /products
SELECT id, itemName, price, tags, category, stock, createdAt
FROM Product
WHERE category ILIKE '%Electronics%'
  AND price BETWEEN 10000 AND 100000
  AND tags && ARRAY['smartphone']
ORDER BY createdAt DESC
LIMIT 20 OFFSET 0;
```

#### 2. User Cart Retrieval
```sql
-- Generated by Prisma for GET /cart
SELECT
  ci.id, ci.quantity, ci.createdAt,
  p.id as product_id, p.itemName, p.price, p.stock
FROM CartItem ci
JOIN Product p ON ci.productId = p.id
WHERE ci.userId = $1 AND ci.isOrdered = false
ORDER BY ci.createdAt DESC;
```

#### 3. Product Search
```sql
-- Generated by Prisma for GET /products/search
SELECT id, itemName, price, tags, category, stock
FROM Product
WHERE (
  itemName ILIKE '%search_term%' OR
  description ILIKE '%search_term%' OR
  category ILIKE '%search_term%' OR
  'search_term' = ANY(tags)
)
ORDER BY itemName ASC
LIMIT 20 OFFSET 0;
```

### Performance Considerations

#### Recommended Indexes
```sql
-- User table indexes
CREATE INDEX idx_user_email ON User(email);

-- Product table indexes
CREATE INDEX idx_product_category ON Product(category);
CREATE INDEX idx_product_tags ON Product USING GIN(tags);
CREATE INDEX idx_product_price ON Product(price);
CREATE INDEX idx_product_stock ON Product(stock);

-- CartItem table indexes
CREATE INDEX idx_cartitem_user ON CartItem(userId);
CREATE INDEX idx_cartitem_product ON CartItem(productId);
CREATE INDEX idx_cartitem_ordered ON CartItem(isOrdered);
CREATE UNIQUE INDEX idx_cartitem_active ON CartItem(userId, productId)
WHERE isOrdered = false;

-- Order table indexes
CREATE INDEX idx_order_cartitem ON Order(cartItemId);
CREATE INDEX idx_order_date ON Order(orderedAt);
```

## Database Design Decisions

### Why This Schema Design?

1. **Normalization**: The schema follows 3NF to minimize data redundancy
2. **Scalability**: Foreign key relationships allow for efficient joins
3. **Flexibility**: Tags array allows dynamic product categorization
4. **Performance**: Strategic indexes support common query patterns
5. **Data Integrity**: Constraints ensure business rule enforcement

### Trade-offs and Considerations

**Advantages**:
- Clear separation of concerns
- Type safety with Prisma
- Efficient for read-heavy workloads
- Supports complex filtering and search

**Potential Improvements**:
- Consider product variants table for size/color options
- Add product images table for multiple photos
- Implement soft deletes for audit trails
- Add user roles and permissions table

### Migration Strategy

The database uses Prisma migrations for version control:

```bash
# Generate migration
npx prisma migrate dev --name add_new_feature

# Apply to production
npx prisma migrate deploy

# Reset development database
npx prisma migrate reset
```

---

## Related Documentation

- [API Documentation](api.md) - Complete API endpoint documentation
- [README.md](../README.md) - Project setup and overview
- [Prisma Schema](../prisma/schema.prisma) - Database schema definition
