# Product Data Flow and Structure

This document provides detailed visualizations of how product data flows through the e-commerce API system, including data structures, API operations, and business logic.

## Table of Contents
- [Product Data Dimensions](#product-data-dimensions)
- [API Data Flow](#api-data-flow)
- [Product Operations](#product-operations)
- [Search and Filtering](#search-and-filtering)
- [Inventory Management](#inventory-management)

## Product Data Dimensions

### Core Product Structure

```mermaid
graph TD
    A[Product Entity] --> B[Identification]
    A --> C[Commercial Data]
    A --> D[Descriptive Data]
    A --> E[Inventory Data]
    A --> F[Metadata]
    
    B --> B1[ID: Primary Key]
    B --> B2[Item Name: Display Name]
    
    C --> C1[Price: Integer cents]
    C --> C2[Category: String]
    C --> C3[Currency: USD default]
    
    D --> D1[Description: Text]
    D --> D2[Tags: String Array]
    D --> D3[Brand: Derived from tags]
    
    E --> E1[Stock: Integer count]
    E --> E2[Available: Computed]
    E --> E3[Reserved: Cart items]
    
    F --> F1[Created At: Timestamp]
    F --> F2[Updated At: Timestamp]
    F --> F3[Status: Active/Inactive]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#f1f8e9
```

### Data Type Specifications

```mermaid
classDiagram
    class Product {
        +int id
        +string itemName
        +string description
        +int price
        +string category
        +string[] tags
        +int stock
        +DateTime createdAt
        +DateTime updatedAt
        
        +isAvailable() boolean
        +getFormattedPrice() string
        +hasTag(tag) boolean
        +isInCategory(category) boolean
    }
    
    class ProductResponse {
        +boolean success
        +Product data
        +string message
        
        +toJSON() object
    }
    
    class ProductListResponse {
        +boolean success
        +Product[] data
        +Pagination pagination
        
        +toJSON() object
    }
    
    class Pagination {
        +int page
        +int limit
        +int total
        +int totalPages
        
        +hasNext() boolean
        +hasPrevious() boolean
    }
    
    Product --> ProductResponse
    Product --> ProductListResponse
    ProductListResponse --> Pagination
```

## API Data Flow

### Product Retrieval Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant R as Router
    participant M as Middleware
    participant PC as Product Controller
    participant P as Prisma ORM
    participant DB as PostgreSQL
    participant RF as Response Formatter
    
    C->>R: GET /api/v1/products
    R->>M: Rate limiting check
    M->>PC: Route to controller
    PC->>PC: Parse query parameters
    PC->>PC: Build filter criteria
    PC->>P: Execute query with filters
    P->>DB: SQL query with WHERE/ORDER/LIMIT
    DB-->>P: Raw product data
    P-->>PC: Typed product objects
    PC->>RF: Format response
    RF->>PC: Standardized response
    PC-->>C: JSON response with pagination
```

### Product Search Flow

```mermaid
flowchart TD
    A[Search Request] --> B{Search Type}
    
    B -->|Text Query| C[Text Search]
    B -->|Category Filter| D[Category Filter]
    B -->|Tag Filter| E[Tag Filter]
    B -->|Price Range| F[Price Filter]
    
    C --> G[Search Logic]
    G --> G1[Name Match: ILIKE]
    G --> G2[Description Match: ILIKE]
    G --> G3[Tag Array Match: ANY]
    
    D --> H[Category Logic]
    H --> H1[Exact Match]
    H --> H2[Case Insensitive]
    
    E --> I[Tag Logic]
    I --> I1[Array Contains]
    I --> I2[Multiple Tag AND/OR]
    
    F --> J[Price Logic]
    J --> J1[Min Price: >=]
    J --> J2[Max Price: <=]
    J --> J3[Range: BETWEEN]
    
    G1 --> K[Combine Filters]
    G2 --> K
    G3 --> K
    H2 --> K
    I2 --> K
    J3 --> K
    
    K --> L[Database Query]
    L --> M[Apply Pagination]
    M --> N[Sort Results]
    N --> O[Format Response]
    
    style A fill:#ffeb3b
    style K fill:#4caf50
    style O fill:#2196f3
```

## Product Operations

### CRUD Operations Flow

```mermaid
graph LR
    A[Client Request] --> B{HTTP Method}
    
    B -->|GET| C[Read Operations]
    B -->|POST| D[Create Operations]
    B -->|PUT| E[Update Operations]
    B -->|DELETE| F[Delete Operations]
    
    C --> C1[List Products]
    C --> C2[Get Single Product]
    C --> C3[Search Products]
    C --> C4[Filter by Category]
    
    D --> D1[Validate Input]
    D1 --> D2[Check Permissions]
    D2 --> D3[Create Product]
    D3 --> D4[Return Created Product]
    
    E --> E1[Validate Input]
    E1 --> E2[Check Permissions]
    E2 --> E3[Find Existing Product]
    E3 --> E4[Update Fields]
    E4 --> E5[Return Updated Product]
    
    F --> F1[Check Permissions]
    F1 --> F2[Check Dependencies]
    F2 --> F3[Soft/Hard Delete]
    F3 --> F4[Return Success]
    
    G[Database Layer] --> G1[Prisma ORM]
    G1 --> G2[PostgreSQL]
    
    C4 --> G
    D4 --> G
    E5 --> G
    F4 --> G
    
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#e3f2fd
    style F fill:#ffebee
```

### Product Validation Pipeline

```mermaid
flowchart TD
    A[Product Input] --> B[Input Validation]
    
    B --> B1{Required Fields}
    B1 -->|Missing| B2[Return 400 Error]
    B1 -->|Valid| C[Data Type Validation]
    
    C --> C1{Price Validation}
    C1 -->|Invalid| C2[Return 400 Error]
    C1 -->|Valid| D[Business Logic Validation]
    
    D --> D1{Stock Validation}
    D1 -->|Negative| D2[Return 400 Error]
    D1 -->|Valid| E[Database Validation]
    
    E --> E1{Duplicate Check}
    E1 -->|Duplicate| E2[Return 409 Error]
    E1 -->|Unique| F[Save to Database]
    
    F --> G[Success Response]
    
    B2 --> H[Error Response]
    C2 --> H
    D2 --> H
    E2 --> H
    
    style A fill:#e3f2fd
    style G fill:#e8f5e8
    style H fill:#ffebee
```

## Search and Filtering

### Search Algorithm

```mermaid
graph TD
    A[Search Query] --> B[Parse Parameters]
    
    B --> C[Query String: q]
    B --> D[Category Filter]
    B --> E[Tag Filters]
    B --> F[Price Range]
    B --> G[Pagination]
    
    C --> H[Text Search Logic]
    H --> H1[Split into terms]
    H --> H2[Search in itemName]
    H --> H3[Search in description]
    H --> H4[Search in tags array]
    
    D --> I[Category Logic]
    I --> I1[Case insensitive match]
    I --> I2[Partial match support]
    
    E --> J[Tag Logic]
    J --> J1[Array intersection]
    J --> J2[Multiple tag support]
    
    F --> K[Price Logic]
    K --> K1[Convert to cents]
    K --> K2[Range validation]
    
    H4 --> L[Combine Conditions]
    I2 --> L
    J2 --> L
    K2 --> L
    
    L --> M[Build SQL WHERE]
    M --> N[Apply ORDER BY]
    N --> O[Apply LIMIT/OFFSET]
    O --> P[Execute Query]
    
    style A fill:#ffeb3b
    style L fill:#4caf50
    style P fill:#2196f3
```

### Filter Combinations

```mermaid
graph LR
    A[Multiple Filters] --> B[AND Logic]
    
    B --> C[Category AND Price]
    B --> D[Tags AND Category]
    B --> E[Text AND Price Range]
    B --> F[All Filters Combined]
    
    C --> G[WHERE category = 'Electronics' AND price BETWEEN min AND max]
    D --> H[WHERE 'tag' = ANY(tags) AND category = 'category']
    E --> I[WHERE (name ILIKE '%text%' OR description ILIKE '%text%') AND price BETWEEN min AND max]
    F --> J[Complex WHERE clause with all conditions]
    
    style A fill:#e3f2fd
    style B fill:#fff3e0
    style G fill:#e8f5e8
    style H fill:#e8f5e8
    style I fill:#e8f5e8
    style J fill:#e8f5e8
```

## Inventory Management

### Stock Tracking Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant Cart as Cart Controller
    participant P as Product Service
    participant DB as Database
    
    C->>Cart: Add to cart (productId, quantity)
    Cart->>P: Check stock availability
    P->>DB: SELECT stock FROM Product WHERE id = ?
    DB-->>P: Current stock count
    P->>P: Validate quantity <= stock
    alt Stock sufficient
        P-->>Cart: Stock available
        Cart->>DB: Create/Update CartItem
        Note over DB: Stock not reserved until order
    else Insufficient stock
        P-->>Cart: Insufficient stock error
        Cart-->>C: 400 Error response
    end
```

### Stock Reservation Logic

```mermaid
graph TD
    A[Product Stock: 100] --> B[Cart Items Created]
    
    B --> C[User A: 5 items]
    B --> D[User B: 3 items]
    B --> E[User C: 2 items]
    
    F[Available Stock] --> G[Physical Stock: 100]
    F --> H[Reserved in Carts: 10]
    F --> I[Available for Sale: 90]
    
    J[Order Placement] --> K[CartItem.isOrdered = true]
    K --> L[Stock Permanently Allocated]
    
    M[Stock Check Logic] --> N{Requested Quantity}
    N -->|<= Available| O[Allow Addition]
    N -->|> Available| P[Reject with Error]
    
    style A fill:#e8f5e8
    style F fill:#fff3e0
    style J fill:#e3f2fd
    style M fill:#ffeb3b
```

---

## Integration Points

### API Response Examples

**Single Product Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "itemName": "iPhone 15 Pro",
    "description": "Latest iPhone with titanium design",
    "price": 99900,
    "category": "Electronics",
    "tags": ["phone", "apple", "smartphone"],
    "stock": 50,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**Product List Response:**
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}
```

---

## Related Documentation

- [Database Schema](database.md) - Complete database documentation
- [API Documentation](api.md) - API endpoint specifications
- [README.md](../README.md) - Project overview and setup
