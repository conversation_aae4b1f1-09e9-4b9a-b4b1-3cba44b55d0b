# System Architecture Documentation

This document provides a comprehensive overview of the e-commerce API system architecture, including component relationships, data flow, and deployment considerations.

## Table of Contents
- [System Overview](#system-overview)
- [Architecture Layers](#architecture-layers)
- [Component Interactions](#component-interactions)
- [Security Architecture](#security-architecture)
- [Deployment Architecture](#deployment-architecture)

## System Overview

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Mobile App]
        B[Web App]
        C[Admin Panel]
    end
    
    subgraph "API Gateway Layer"
        D[Load Balancer]
        E[Rate Limiter]
        F[CORS Handler]
    end
    
    subgraph "Application Layer"
        G[Express.js Server]
        H[Authentication Middleware]
        I[Route Handlers]
        J[Business Logic Controllers]
    end
    
    subgraph "Data Access Layer"
        K[Prisma ORM]
        L[Connection Pool]
    end
    
    subgraph "Database Layer"
        M[(PostgreSQL)]
        N[Indexes]
        O[Constraints]
    end
    
    subgraph "External Services"
        P[JWT Service]
        Q[Password Hashing]
        R[Logging Service]
    end
    
    A --> D
    B --> D
    C --> D
    
    D --> E
    E --> F
    F --> G
    
    G --> H
    H --> I
    I --> J
    
    J --> K
    K --> L
    L --> M
    
    M --> N
    M --> O
    
    J --> P
    J --> Q
    J --> R
    
    style A fill:#e3f2fd
    style G fill:#e8f5e8
    style M fill:#fff3e0
```

## Architecture Layers

### 1. Presentation Layer (API Interface)

```mermaid
graph LR
    A[HTTP Requests] --> B[Express Router]
    B --> C[Middleware Stack]
    
    C --> D[Security Middleware]
    C --> E[Authentication]
    C --> F[Rate Limiting]
    C --> G[CORS]
    
    D --> H[Route Controllers]
    E --> H
    F --> H
    G --> H
    
    H --> I[Response Formatting]
    I --> J[HTTP Responses]
    
    style A fill:#ffeb3b
    style H fill:#4caf50
    style J fill:#2196f3
```

### 2. Business Logic Layer

```mermaid
graph TD
    A[Controllers] --> B[Authentication Controller]
    A --> C[User Controller]
    A --> D[Product Controller]
    A --> E[Cart Controller]
    
    B --> F[User Registration]
    B --> G[User Login]
    B --> H[Token Management]
    
    C --> I[Profile Management]
    C --> J[User Administration]
    
    D --> K[Product CRUD]
    D --> L[Search & Filter]
    D --> M[Inventory Management]
    
    E --> N[Cart Operations]
    E --> O[Order Processing]
    
    F --> P[Business Rules]
    G --> P
    H --> P
    I --> P
    J --> P
    K --> P
    L --> P
    M --> P
    N --> P
    O --> P
    
    style A fill:#e3f2fd
    style P fill:#e8f5e8
```

### 3. Data Access Layer

```mermaid
graph LR
    A[Controllers] --> B[Prisma Client]
    
    B --> C[Query Builder]
    B --> D[Type Safety]
    B --> E[Connection Management]
    
    C --> F[SQL Generation]
    D --> G[TypeScript Types]
    E --> H[Connection Pool]
    
    F --> I[(PostgreSQL)]
    G --> J[Compile-time Checks]
    H --> I
    
    style A fill:#e3f2fd
    style B fill:#fff3e0
    style I fill:#4caf50
```

## Component Interactions

### Request Processing Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant LB as Load Balancer
    participant MW as Middleware
    participant RT as Router
    participant CT as Controller
    participant PR as Prisma
    participant DB as Database
    
    C->>LB: HTTP Request
    LB->>MW: Forward Request
    MW->>MW: Rate Limiting
    MW->>MW: CORS Check
    MW->>MW: Authentication
    MW->>RT: Validated Request
    RT->>CT: Route to Controller
    CT->>CT: Business Logic
    CT->>PR: Database Query
    PR->>DB: SQL Query
    DB-->>PR: Query Results
    PR-->>CT: Typed Results
    CT->>CT: Format Response
    CT-->>C: JSON Response
```

### Authentication Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant A as Auth Controller
    participant H as Hash Service
    participant J as JWT Service
    participant DB as Database
    
    Note over C,DB: Registration Flow
    C->>A: POST /auth/register
    A->>H: Hash password
    H-->>A: Hashed password
    A->>DB: Store user
    DB-->>A: User created
    A->>J: Generate tokens
    J-->>A: Access + Refresh tokens
    A-->>C: User data + tokens
    
    Note over C,DB: Login Flow
    C->>A: POST /auth/login
    A->>DB: Find user by email
    DB-->>A: User data
    A->>H: Verify password
    H-->>A: Password valid
    A->>J: Generate tokens
    J-->>A: Access + Refresh tokens
    A-->>C: User data + tokens
```

## Security Architecture

### Security Layers

```mermaid
graph TD
    A[Security Layers] --> B[Network Security]
    A --> C[Application Security]
    A --> D[Data Security]
    A --> E[Authentication Security]
    
    B --> B1[HTTPS/TLS]
    B --> B2[Rate Limiting]
    B --> B3[CORS Policy]
    
    C --> C1[Helmet Headers]
    C --> C2[Input Validation]
    C --> C3[Error Handling]
    
    D --> D1[SQL Injection Protection]
    D --> D2[Data Encryption]
    D --> D3[Access Controls]
    
    E --> E1[JWT Tokens]
    E --> E2[Password Hashing]
    E --> E3[Token Expiration]
    
    style A fill:#ffebee
    style B fill:#e8f5e8
    style C fill:#e3f2fd
    style D fill:#fff3e0
    style E fill:#f3e5f5
```

### JWT Token Flow

```mermaid
graph LR
    A[User Login] --> B[Generate Access Token]
    A --> C[Generate Refresh Token]
    
    B --> D[15 min expiry]
    C --> E[7 day expiry]
    
    D --> F[API Requests]
    F --> G{Token Valid?}
    
    G -->|Yes| H[Process Request]
    G -->|Expired| I[Return 401]
    
    I --> J[Use Refresh Token]
    J --> K[Generate New Access Token]
    K --> F
    
    E --> L{Refresh Valid?}
    L -->|Yes| K
    L -->|Expired| M[Re-login Required]
    
    style A fill:#e3f2fd
    style H fill:#e8f5e8
    style M fill:#ffebee
```

## Deployment Architecture

### Development Environment

```mermaid
graph TB
    subgraph "Developer Machine"
        A[Node.js App]
        B[Docker Compose]
        C[PostgreSQL Container]
        D[Development Tools]
    end
    
    A --> B
    B --> C
    A --> D
    
    D --> E[Nodemon]
    D --> F[Prisma Studio]
    D --> G[API Testing]
    
    style A fill:#e3f2fd
    style C fill:#e8f5e8
```

### Production Environment

```mermaid
graph TB
    subgraph "Load Balancer"
        A[Nginx/ALB]
    end
    
    subgraph "Application Tier"
        B[Node.js Instance 1]
        C[Node.js Instance 2]
        D[Node.js Instance N]
    end
    
    subgraph "Database Tier"
        E[PostgreSQL Primary]
        F[PostgreSQL Replica]
    end
    
    subgraph "Monitoring"
        G[Logging Service]
        H[Metrics Collection]
        I[Health Checks]
    end
    
    A --> B
    A --> C
    A --> D
    
    B --> E
    C --> E
    D --> E
    
    E --> F
    
    B --> G
    C --> G
    D --> G
    
    B --> H
    C --> H
    D --> H
    
    A --> I
    
    style A fill:#ffeb3b
    style E fill:#4caf50
    style G fill:#ff9800
```

### Container Architecture

```mermaid
graph TB
    subgraph "Docker Environment"
        A[API Container]
        B[Database Container]
        C[Redis Container]
    end
    
    subgraph "API Container"
        D[Node.js Runtime]
        E[Express App]
        F[Prisma Client]
    end
    
    subgraph "Database Container"
        G[PostgreSQL 15]
        H[Data Volume]
    end
    
    A --> D
    D --> E
    E --> F
    
    B --> G
    G --> H
    
    F --> G
    
    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
```

## Performance Considerations

### Database Optimization

```mermaid
graph TD
    A[Query Optimization] --> B[Indexes]
    A --> C[Connection Pooling]
    A --> D[Query Patterns]
    
    B --> B1[Primary Keys]
    B --> B2[Foreign Keys]
    B --> B3[Search Indexes]
    B --> B4[Composite Indexes]
    
    C --> C1[Connection Limits]
    C --> C2[Connection Reuse]
    C --> C3[Pool Management]
    
    D --> D1[Selective Queries]
    D --> D2[Pagination]
    D --> D3[Eager Loading]
    
    style A fill:#e3f2fd
    style B fill:#e8f5e8
    style C fill:#fff3e0
    style D fill:#f3e5f5
```

### Caching Strategy

```mermaid
graph LR
    A[Client Request] --> B{Cache Check}
    
    B -->|Hit| C[Return Cached Data]
    B -->|Miss| D[Database Query]
    
    D --> E[Process Data]
    E --> F[Store in Cache]
    F --> G[Return Data]
    
    H[Cache Layers] --> I[Application Cache]
    H --> J[Database Query Cache]
    H --> K[CDN Cache]
    
    style A fill:#ffeb3b
    style C fill:#e8f5e8
    style D fill:#fff3e0
```

## Scalability Patterns

### Horizontal Scaling

```mermaid
graph TB
    A[Load Balancer] --> B[API Instance 1]
    A --> C[API Instance 2]
    A --> D[API Instance 3]
    
    B --> E[Shared Database]
    C --> E
    D --> E
    
    F[Session Store] --> G[Redis Cluster]
    
    B --> G
    C --> G
    D --> G
    
    style A fill:#ffeb3b
    style E fill:#4caf50
    style G fill:#ff9800
```

---

## Related Documentation

- [Database Schema](database.md) - Database design and relationships
- [Product Data Flow](product-data-flow.md) - Product-specific architecture
- [API Documentation](api.md) - API endpoint specifications
- [README.md](../README.md) - Project setup and overview
