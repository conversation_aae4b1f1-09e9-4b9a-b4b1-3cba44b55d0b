# E-commerce API

A simple Node.js e-commerce API built with Express.js, Prisma ORM, and PostgreSQL.

## 🚀 Features

- **User Management**: Create, read, update, delete users
- **Product Management**: Manage products with tags and pricing
- **Shopping Cart**: Add/remove items, update quantities
- **Order Management**: Create orders from cart items
- **Search & Filter**: Search products by name/tags, filter by price

## 📁 Project Structure

```
ecommerce-app/
│
├── prisma/
│   └── schema.prisma       # Database schema
│
├── src/
│   ├── controllers/        # Business logic
│   │   ├── userController.js
│   │   ├── productController.js
│   │   └── cartController.js
│   │
│   ├── routes/            # API routes
│   │   ├── userRoutes.js
│   │   ├── productRoutes.js
│   │   └── cartRoutes.js
│   │
│   ├── app.js             # Express app setup
│   └── server.js          # Server startup
│
├── .env                   # Environment variables
├── package.json
└── README.md
```

## 🛠️ Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- PostgreSQL database
- npm or yarn

### Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   Update the `.env` file with your database credentials:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/ecommerce"
   PORT=3000
   ```

3. **Set up the database:**
   ```bash
   # Generate Prisma client
   npm run db:generate
   
   # Push schema to database (for development)
   npm run db:push
   
   # Or run migrations (for production)
   npm run db:migrate
   ```

4. **Start the server:**
   ```bash
   # Development mode (with nodemon)
   npm run dev
   
   # Production mode
   npm start
   ```

The server will start on `http://localhost:3000`

## 📚 API Endpoints

### Health Check
- `GET /health` - Check if API is running

### Users
- `POST /api/users` - Create a new user
- `GET /api/users` - Get all users
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

### Products
- `POST /api/products` - Create a new product
- `GET /api/products` - Get all products (supports filtering)
- `GET /api/products/search?q=query` - Search products
- `GET /api/products/:id` - Get product by ID
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product

### Cart & Orders
- `POST /api/cart` - Add item to cart
- `GET /api/cart/user/:userId` - Get user's cart
- `PUT /api/cart/:id` - Update cart item quantity
- `DELETE /api/cart/:id` - Remove item from cart
- `DELETE /api/cart/user/:userId` - Clear user's cart
- `POST /api/cart/order/:id` - Create order from cart item
- `GET /api/cart/orders` - Get all orders
- `GET /api/cart/orders/user/:userId` - Get user's orders
- `PUT /api/cart/orders/:id/notify` - Mark order as notified

## 🧪 Example API Calls

### Create a User
```bash
curl -X POST http://localhost:3000/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "address": {"street": "123 Main St", "city": "New York", "zip": "10001"},
    "phoneNumber": "+1234567890",
    "email": "<EMAIL>"
  }'
```

### Create a Product
```bash
curl -X POST http://localhost:3000/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "itemName": "Laptop",
    "tags": ["electronics", "computer"],
    "price": 999
  }'
```

### Add to Cart
```bash
curl -X POST http://localhost:3000/api/cart \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 1,
    "productId": 1,
    "quantity": 2
  }'
```

### Create Order
```bash
curl -X POST http://localhost:3000/api/cart/order/1
```

## 🗄️ Database Schema

The application uses the following main entities:

- **User**: Stores user information with JSON address field
- **Product**: Products with tags array and pricing
- **CartItem**: Links users to products with quantities
- **Order**: Created from cart items when ordered

## 🔧 Available Scripts

- `npm start` - Start the production server
- `npm run dev` - Start development server with nodemon
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema changes to database
- `npm run db:migrate` - Run database migrations

## 🚀 Deployment

For production deployment:

1. Set up a PostgreSQL database
2. Update the `DATABASE_URL` in your environment
3. Run `npm run db:migrate` to apply migrations
4. Start the server with `npm start`

## 📝 Notes

- The API includes CORS headers for development
- All prices are stored as integers (cents)
- Cart items are automatically merged if the same product is added multiple times
- Orders are created from individual cart items, not entire carts
- The address field in User model uses JSON type for flexibility