# E-commerce API v1

A production-ready RESTful API for e-commerce applications built with Node.js, Express, Prisma, and PostgreSQL.

## 🚀 Features

- **JWT Authentication** with access/refresh tokens
- **Rate Limiting** for API protection
- **Versioned API** endpoints (`/api/v1/`)
- **Comprehensive Security** with Helmet and CORS
- **Optimized Database Queries** with Prisma
- **Pagination Support** for large datasets
- **Input Validation** and error handling
- **Complete API Documentation**

## 🛠️ Tech Stack

- **Backend**: Node.js, Express.js
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT (JSON Web Tokens)
- **Security**: Helmet, CORS, Rate Limiting
- **Password Hashing**: bcryptjs
- **Development**: Nodemon for hot reload

## 📋 Prerequisites

Before you begin, ensure you have the following installed:
- Node.js (v16 or higher)
- npm or yarn
- One of the following for database:
  - Docker & Docker Compose (Recommended)
  - PostgreSQL installed locally

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Run the automated setup script:**
   ```bash
   ./setup-database.sh
   ```

   This script will:
   - Set up PostgreSQL (Docker or local)
   - Initialize the database schema
   - Seed with sample data
   - Provide demo login credentials

3. **Start the development server:**
   ```bash
   npm run dev
   ```

### Option 2: Manual Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Set up PostgreSQL:**

   **Using Docker Compose (Recommended):**
   ```bash
   docker-compose up -d db
   ```

   **Using Local PostgreSQL:**
   ```bash
   # Create database
   createdb ecommerce
   ```

3. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

4. **Initialize database:**
   ```bash
   npm run db:generate  # Generate Prisma client
   npm run db:push      # Create database tables
   npm run db:seed      # Seed with sample data
   ```

5. **Start the server:**
   ```bash
   npm run dev
   ```

## 🔧 Environment Variables

Create a `.env` file in the root directory:

```env
# Database
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/ecommerce?schema=public"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# Server
PORT=3000
NODE_ENV=development

# CORS Configuration
MOBILE_APP_URL="http://localhost:3001"
```

## 📚 Documentation

### API Documentation
The API is fully documented with examples. See [docs/api.md](docs/api.md) for complete endpoint documentation.

### Database Documentation
Comprehensive database schema and relationship documentation with visual diagrams:
- **[Database Schema](docs/database.md)** - Complete database documentation with ER diagrams
- **[Product Data Flow](docs/product-data-flow.md)** - Product operations and data flow visualizations
- **[System Architecture](docs/architecture.md)** - Complete system architecture and component interactions

### Visual Database Schema

```mermaid
erDiagram
    USER {
        int id PK
        string email UK
        string password
        string firstName
        string lastName
        datetime createdAt
        datetime updatedAt
    }

    PRODUCT {
        int id PK
        string itemName
        string description
        int price
        string category
        string[] tags
        int stock
        datetime createdAt
        datetime updatedAt
    }

    CARTITEM {
        int id PK
        int userId FK
        int productId FK
        int quantity
        boolean isOrdered
        datetime createdAt
        datetime updatedAt
    }

    ORDER {
        int id PK
        int cartItemId FK
        boolean notified
        datetime orderedAt
    }

    USER ||--o{ CARTITEM : "owns"
    PRODUCT ||--o{ CARTITEM : "contains"
    CARTITEM ||--o| ORDER : "becomes"
```

### Base URL
```
http://localhost:3000/api/v1
```

### Quick Test
```bash
# Health check
curl http://localhost:3000/health

# Get products
curl http://localhost:3000/api/v1/products

# Register user
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","firstName":"Test","lastName":"User"}'
```

## 🔑 Demo Credentials

After seeding, you can use these demo accounts:

- **Email**: `<EMAIL>`
- **Email**: `<EMAIL>`
- **Email**: `<EMAIL>`
- **Email**: `<EMAIL>`
- **Password**: `password123` (for all users)

## 🗄️ Database Design

The e-commerce API uses a normalized PostgreSQL database with the following key design principles:

### Core Entities & Relationships
- **Users**: Authentication and profile management
- **Products**: Catalog with categories, tags, and inventory
- **CartItems**: Many-to-many relationship between users and products
- **Orders**: Order tracking and fulfillment

### Key Features
- **Referential Integrity**: Foreign key constraints ensure data consistency
- **Optimized Queries**: Strategic indexes for search and filtering
- **Type Safety**: Prisma ORM provides compile-time type checking
- **Scalable Design**: Normalized structure supports growth

For detailed database documentation including ER diagrams, query patterns, and performance considerations, see [docs/database.md](docs/database.md).

## 📁 Project Structure

```
ecommerce-app/
├── docs/
│   ├── README.md                # Documentation index & navigation
│   ├── api.md                   # Complete API endpoint documentation
│   ├── database.md              # Database schema, ER diagrams & relationships
│   ├── architecture.md          # System architecture & component interactions
│   └── product-data-flow.md     # Product operations & data flow visualizations
├── prisma/
│   ├── schema.prisma            # Database schema
│   └── seed.js                  # Database seeding
├── src/
│   ├── controllers/             # Business logic
│   │   ├── authController.js
│   │   ├── userController.js
│   │   ├── productController.js
│   │   └── cartController.js
│   ├── middleware/              # Custom middleware
│   │   ├── auth.js
│   │   ├── errorHandler.js
│   │   └── rateLimiter.js
│   ├── routes/v1/               # API routes
│   │   ├── authRoutes.js
│   │   ├── userRoutes.js
│   │   ├── productRoutes.js
│   │   └── cartRoutes.js
│   ├── utils/                   # Utility functions
│   │   └── response.js
│   ├── app.js                   # Express app setup
│   └── server.js                # Server entry point
├── docker-compose.yml           # Docker setup
├── setup-database.sh           # Automated setup script
└── package.json
```

## 🔒 Security Features

- **JWT Authentication** with secure token handling
- **Rate Limiting** (100 requests/15min, 5 auth requests/15min)
- **Helmet** for security headers
- **CORS** configuration for cross-origin requests
- **Input validation** and sanitization
- **Password hashing** with bcryptjs
- **SQL injection protection** via Prisma

## 🧪 Available Scripts

```bash
npm run dev          # Start development server with hot reload
npm run start        # Start production server
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema to database
npm run db:migrate   # Create and run migrations
npm run db:seed      # Seed database with sample data
```

## 🐳 Docker Support

The project includes Docker Compose configuration for easy PostgreSQL setup:

```bash
# Start PostgreSQL
docker-compose up -d db

# Stop PostgreSQL
docker-compose down

# View logs
docker-compose logs db
```

## 🔍 API Endpoints Overview

### Authentication
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - User logout

### Users
- `GET /api/v1/users/profile` - Get current user profile
- `PUT /api/v1/users/profile` - Update current user profile

### Products
- `GET /api/v1/products` - Get all products (with pagination)
- `GET /api/v1/products/:id` - Get product by ID
- `GET /api/v1/products/search` - Search products
- `GET /api/v1/products/category/:category` - Get products by category

### Cart
- `GET /api/v1/cart` - Get user's cart
- `POST /api/v1/cart` - Add item to cart
- `PUT /api/v1/cart/:itemId` - Update cart item
- `DELETE /api/v1/cart/:itemId` - Remove item from cart
- `DELETE /api/v1/cart` - Clear cart

## 📄 License

This project is licensed under the MIT License.