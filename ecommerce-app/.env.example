# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/ecommerce_db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# Server Configuration
PORT=3000
NODE_ENV=development

# CORS Configuration
MOBILE_APP_URL="http://localhost:3001"

# Rate Limiting (requests per window)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX_REQUESTS=5
