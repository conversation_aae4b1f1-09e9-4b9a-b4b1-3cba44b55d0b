/**
 * Centralized Error Handler Middleware
 * Provides consistent error responses for mobile clients
 */
const errorHandler = (err, req, res, next) => {
  console.error('Error:', err);

  // Default error
  let error = {
    success: false,
    error: 'Internal server error'
  };

  // Prisma errors
  if (err.code === 'P2002') {
    error.error = 'Duplicate entry - resource already exists';
    return res.status(409).json(error);
  }

  if (err.code === 'P2025') {
    error.error = 'Resource not found';
    return res.status(404).json(error);
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error.error = 'Invalid token';
    return res.status(401).json(error);
  }

  if (err.name === 'TokenExpiredError') {
    error.error = 'Token expired';
    return res.status(401).json(error);
  }

  // Validation errors
  if (err.name === 'ValidationError') {
    error.error = err.message;
    return res.status(400).json(error);
  }

  // Custom errors with statusCode
  if (err.statusCode) {
    error.error = err.message || 'An error occurred';
    return res.status(err.statusCode).json(error);
  }

  // Default 500 error
  res.status(500).json(error);
};

module.exports = errorHandler;
