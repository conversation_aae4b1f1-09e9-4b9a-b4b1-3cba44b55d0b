const express = require('express');
const router = express.Router();
const {
  addToCart,
  getUserCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  createOrder,
  getAllOrders,
  getUserOrders,
  markOrderNotified
} = require('../controllers/cartController');

// POST /api/cart - Add item to cart
router.post('/', addToCart);

// GET /api/cart/user/:userId - Get user's cart
router.get('/user/:userId', getUserCart);

// PUT /api/cart/:id - Update cart item quantity
router.put('/:id', updateCartItem);

// DELETE /api/cart/:id - Remove item from cart
router.delete('/:id', removeFromCart);

// DELETE /api/cart/user/:userId - Clear user's cart
router.delete('/user/:userId', clearCart);

// POST /api/cart/order/:id - Create order from cart item
router.post('/order/:id', createOrder);

// GET /api/cart/orders - Get all orders
router.get('/orders', getAllOrders);

// GET /api/cart/orders/user/:userId - Get user's orders
router.get('/orders/user/:userId', getUserOrders);

// PUT /api/cart/orders/:id/notify - Mark order as notified
router.put('/orders/:id/notify', markOrderNotified);

module.exports = router;