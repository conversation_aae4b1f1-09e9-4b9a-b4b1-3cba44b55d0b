const express = require('express');
const router = express.Router();
const { authRateLimiter } = require('../../middleware/rateLimiter');
const {
  register,
  login,
  refreshToken,
  logout
} = require('../../controllers/authController');

// POST /api/v1/auth/register - Register new user
router.post('/register', authRateLimiter, register);

// POST /api/v1/auth/login - User login
router.post('/login', authRateLimiter, login);

// POST /api/v1/auth/refresh - Refresh access token
router.post('/refresh', refreshToken);

// POST /api/v1/auth/logout - User logout
router.post('/logout', logout);

module.exports = router;
