const express = require('express');
const router = express.Router();
const authenticateToken = require('../../middleware/auth');
const {
  getAllUsers,
  getUserById,
  updateUser,
  deleteUser,
  getUserProfile,
  updateUserProfile
} = require('../../controllers/userController');

// GET /api/v1/users/profile - Get current user profile (protected)
router.get('/profile', authenticateToken, getUserProfile);

// PUT /api/v1/users/profile - Update current user profile (protected)
router.put('/profile', authenticateToken, updateUserProfile);

// GET /api/v1/users - Get all users (protected, admin only)
router.get('/', authenticateToken, getAllUsers);

// GET /api/v1/users/:id - Get user by ID (protected)
router.get('/:id', authenticateToken, getUserById);

// PUT /api/v1/users/:id - Update user (protected, admin only)
router.put('/:id', authenticateToken, updateUser);

// DELETE /api/v1/users/:id - Delete user (protected, admin only)
router.delete('/:id', authenticateToken, deleteUser);

module.exports = router;
