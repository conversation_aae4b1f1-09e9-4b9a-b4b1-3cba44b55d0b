const express = require('express');
const router = express.Router();
const authenticateToken = require('../../middleware/auth');
const {
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  searchProducts,
  getProductsByCategory
} = require('../../controllers/productController');

// GET /api/v1/products - Get all products (public)
router.get('/', getAllProducts);

// GET /api/v1/products/search - Search products (public)
router.get('/search', searchProducts);

// GET /api/v1/products/category/:category - Get products by category (public)
router.get('/category/:category', getProductsByCategory);

// GET /api/v1/products/:id - Get product by ID (public)
router.get('/:id', getProductById);

// POST /api/v1/products - Create product (protected, admin only)
router.post('/', authenticateToken, createProduct);

// PUT /api/v1/products/:id - Update product (protected, admin only)
router.put('/:id', authenticateToken, updateProduct);

// DELETE /api/v1/products/:id - Delete product (protected, admin only)
router.delete('/:id', authenticateToken, deleteProduct);

module.exports = router;
