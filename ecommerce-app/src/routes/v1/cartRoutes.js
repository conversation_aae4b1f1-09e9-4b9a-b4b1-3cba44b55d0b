const express = require('express');
const router = express.Router();
const authenticateToken = require('../../middleware/auth');
const {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart
} = require('../../controllers/cartController');

// All cart routes require authentication
router.use(authenticateToken);

// GET /api/v1/cart - Get user's cart
router.get('/', getCart);

// POST /api/v1/cart - Add item to cart
router.post('/', addToCart);

// PUT /api/v1/cart/:itemId - Update cart item quantity
router.put('/:itemId', updateCartItem);

// DELETE /api/v1/cart/:itemId - Remove item from cart
router.delete('/:itemId', removeFromCart);

// DELETE /api/v1/cart - Clear entire cart
router.delete('/', clearCart);

module.exports = router;
