const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Add item to cart
exports.addToCart = async (req, res) => {
  try {
    const { userId, productId, quantity } = req.body;
    
    if (!userId || !productId || !quantity) {
      return res.status(400).json({ 
        error: 'User ID, product ID, and quantity are required' 
      });
    }

    if (quantity <= 0) {
      return res.status(400).json({ 
        error: 'Quantity must be greater than 0' 
      });
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: parseInt(userId) }
    });
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: parseInt(productId) }
    });
    
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Check if item already exists in cart
    const existingCartItem = await prisma.cartItem.findFirst({
      where: {
        userId: parseInt(userId),
        productId: parseInt(productId),
        isOrdered: false
      }
    });

    let cartItem;
    if (existingCartItem) {
      // Update quantity if item already exists
      cartItem = await prisma.cartItem.update({
        where: { id: existingCartItem.id },
        data: { quantity: existingCartItem.quantity + parseInt(quantity) },
        include: {
          product: true,
          user: true
        }
      });
    } else {
      // Create new cart item
      cartItem = await prisma.cartItem.create({
        data: {
          userId: parseInt(userId),
          productId: parseInt(productId),
          quantity: parseInt(quantity)
        },
        include: {
          product: true,
          user: true
        }
      });
    }
    
    res.status(201).json(cartItem);
  } catch (error) {
    console.error('Error adding to cart:', error);
    res.status(500).json({ error: 'Failed to add item to cart' });
  }
};

// Get user's cart
exports.getUserCart = async (req, res) => {
  try {
    const { userId } = req.params;
    
    const cartItems = await prisma.cartItem.findMany({
      where: {
        userId: parseInt(userId),
        isOrdered: false
      },
      include: {
        product: true,
        user: true
      }
    });
    
    // Calculate total
    const total = cartItems.reduce((sum, item) => {
      return sum + (item.product.price * item.quantity);
    }, 0);
    
    res.json({
      cartItems,
      total,
      itemCount: cartItems.length
    });
  } catch (error) {
    console.error('Error fetching cart:', error);
    res.status(500).json({ error: 'Failed to fetch cart' });
  }
};

// Update cart item quantity
exports.updateCartItem = async (req, res) => {
  try {
    const { id } = req.params;
    const { quantity } = req.body;
    
    if (!quantity || quantity <= 0) {
      return res.status(400).json({ 
        error: 'Quantity must be greater than 0' 
      });
    }

    const cartItem = await prisma.cartItem.update({
      where: { id: parseInt(id) },
      data: { quantity: parseInt(quantity) },
      include: {
        product: true,
        user: true
      }
    });
    
    res.json(cartItem);
  } catch (error) {
    console.error('Error updating cart item:', error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Cart item not found' });
    }
    res.status(500).json({ error: 'Failed to update cart item' });
  }
};

// Remove item from cart
exports.removeFromCart = async (req, res) => {
  try {
    const { id } = req.params;
    
    await prisma.cartItem.delete({
      where: { id: parseInt(id) }
    });
    
    res.status(204).send();
  } catch (error) {
    console.error('Error removing from cart:', error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Cart item not found' });
    }
    res.status(500).json({ error: 'Failed to remove item from cart' });
  }
};

// Clear user's cart
exports.clearCart = async (req, res) => {
  try {
    const { userId } = req.params;
    
    await prisma.cartItem.deleteMany({
      where: {
        userId: parseInt(userId),
        isOrdered: false
      }
    });
    
    res.status(204).send();
  } catch (error) {
    console.error('Error clearing cart:', error);
    res.status(500).json({ error: 'Failed to clear cart' });
  }
};

// Create order from cart item
exports.createOrder = async (req, res) => {
  try {
    const { id } = req.params; // cart item id
    
    // Check if cart item exists and is not already ordered
    const cartItem = await prisma.cartItem.findUnique({
      where: { id: parseInt(id) },
      include: {
        product: true,
        user: true
      }
    });
    
    if (!cartItem) {
      return res.status(404).json({ error: 'Cart item not found' });
    }
    
    if (cartItem.isOrdered) {
      return res.status(400).json({ error: 'Cart item is already ordered' });
    }

    // Create order and mark cart item as ordered
    const result = await prisma.$transaction(async (prisma) => {
      // Update cart item
      const updatedCartItem = await prisma.cartItem.update({
        where: { id: parseInt(id) },
        data: { isOrdered: true }
      });

      // Create order
      const order = await prisma.order.create({
        data: {
          cartItemId: parseInt(id)
        },
        include: {
          cartItem: {
            include: {
              product: true,
              user: true
            }
          }
        }
      });

      return order;
    });
    
    res.status(201).json(result);
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({ error: 'Failed to create order' });
  }
};

// Get all orders
exports.getAllOrders = async (req, res) => {
  try {
    const orders = await prisma.order.findMany({
      include: {
        cartItem: {
          include: {
            product: true,
            user: true
          }
        }
      },
      orderBy: {
        orderedAt: 'desc'
      }
    });
    
    res.json(orders);
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({ error: 'Failed to fetch orders' });
  }
};

// Get user's orders
exports.getUserOrders = async (req, res) => {
  try {
    const { userId } = req.params;
    
    const orders = await prisma.order.findMany({
      where: {
        cartItem: {
          userId: parseInt(userId)
        }
      },
      include: {
        cartItem: {
          include: {
            product: true,
            user: true
          }
        }
      },
      orderBy: {
        orderedAt: 'desc'
      }
    });
    
    res.json(orders);
  } catch (error) {
    console.error('Error fetching user orders:', error);
    res.status(500).json({ error: 'Failed to fetch user orders' });
  }
};

// Mark order as notified
exports.markOrderNotified = async (req, res) => {
  try {
    const { id } = req.params;
    
    const order = await prisma.order.update({
      where: { id: parseInt(id) },
      data: { notified: true },
      include: {
        cartItem: {
          include: {
            product: true,
            user: true
          }
        }
      }
    });
    
    res.json(order);
  } catch (error) {
    console.error('Error marking order as notified:', error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Order not found' });
    }
    res.status(500).json({ error: 'Failed to mark order as notified' });
  }
};