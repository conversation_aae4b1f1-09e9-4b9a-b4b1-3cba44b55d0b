const { PrismaClient } = require('@prisma/client');
const { success, error } = require('../utils/response');

const prisma = new PrismaClient();

/**
 * Get user's cart
 */
const getCart = async (req, res, next) => {
  try {
    const userId = req.user.userId;

    const cartItems = await prisma.cartItem.findMany({
      where: {
        userId: userId,
        isOrdered: false
      },
      select: {
        id: true,
        quantity: true,
        createdAt: true,
        product: {
          select: {
            id: true,
            itemName: true,
            price: true,
            stock: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Calculate totals
    const total = cartItems.reduce((sum, item) => {
      return sum + (item.product.price * item.quantity);
    }, 0);

    const itemCount = cartItems.reduce((sum, item) => {
      return sum + item.quantity;
    }, 0);

    success(res, {
      items: cartItems,
      summary: {
        total,
        itemCount,
        uniqueItems: cartItems.length
      }
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Add item to cart
 */
const addToCart = async (req, res, next) => {
  try {
    const userId = req.user.userId;
    const { productId, quantity = 1 } = req.body;

    if (!productId) {
      return error(res, 400, 'Product ID is required');
    }

    if (quantity <= 0) {
      return error(res, 400, 'Quantity must be greater than 0');
    }

    // Check if product exists and has stock
    const product = await prisma.product.findUnique({
      where: { id: parseInt(productId) },
      select: {
        id: true,
        itemName: true,
        price: true,
        stock: true
      }
    });

    if (!product) {
      return error(res, 404, 'Product not found');
    }

    if (product.stock < quantity) {
      return error(res, 400, 'Insufficient stock available');
    }

    // Check if item already exists in cart
    const existingCartItem = await prisma.cartItem.findFirst({
      where: {
        userId: userId,
        productId: parseInt(productId),
        isOrdered: false
      }
    });

    let cartItem;
    if (existingCartItem) {
      const newQuantity = existingCartItem.quantity + parseInt(quantity);

      if (product.stock < newQuantity) {
        return error(res, 400, 'Insufficient stock for requested quantity');
      }

      // Update quantity if item already exists
      cartItem = await prisma.cartItem.update({
        where: { id: existingCartItem.id },
        data: { quantity: newQuantity },
        select: {
          id: true,
          quantity: true,
          product: {
            select: {
              id: true,
              itemName: true,
              price: true
            }
          }
        }
      });
    } else {
      // Create new cart item
      cartItem = await prisma.cartItem.create({
        data: {
          userId: userId,
          productId: parseInt(productId),
          quantity: parseInt(quantity)
        },
        select: {
          id: true,
          quantity: true,
          product: {
            select: {
              id: true,
              itemName: true,
              price: true
            }
          }
        }
      });
    }

    success(res, cartItem, 'Item added to cart successfully', 201);
  } catch (err) {
    next(err);
  }
};

/**
 * Update cart item quantity
 */
const updateCartItem = async (req, res, next) => {
  try {
    const userId = req.user.userId;
    const { itemId } = req.params;
    const { quantity } = req.body;

    if (!quantity || quantity <= 0) {
      return error(res, 400, 'Quantity must be greater than 0');
    }

    // Check if cart item belongs to user
    const existingItem = await prisma.cartItem.findFirst({
      where: {
        id: parseInt(itemId),
        userId: userId,
        isOrdered: false
      },
      include: {
        product: {
          select: {
            stock: true
          }
        }
      }
    });

    if (!existingItem) {
      return error(res, 404, 'Cart item not found');
    }

    if (existingItem.product.stock < quantity) {
      return error(res, 400, 'Insufficient stock available');
    }

    const cartItem = await prisma.cartItem.update({
      where: { id: parseInt(itemId) },
      data: { quantity: parseInt(quantity) },
      select: {
        id: true,
        quantity: true,
        product: {
          select: {
            id: true,
            itemName: true,
            price: true
          }
        }
      }
    });

    success(res, cartItem, 'Cart item updated successfully');
  } catch (err) {
    next(err);
  }
};

/**
 * Remove item from cart
 */
const removeFromCart = async (req, res, next) => {
  try {
    const userId = req.user.userId;
    const { itemId } = req.params;

    // Check if cart item belongs to user
    const existingItem = await prisma.cartItem.findFirst({
      where: {
        id: parseInt(itemId),
        userId: userId,
        isOrdered: false
      }
    });

    if (!existingItem) {
      return error(res, 404, 'Cart item not found');
    }

    await prisma.cartItem.delete({
      where: { id: parseInt(itemId) }
    });

    success(res, null, 'Item removed from cart successfully');
  } catch (err) {
    next(err);
  }
};

/**
 * Clear user's cart
 */
const clearCart = async (req, res, next) => {
  try {
    const userId = req.user.userId;

    const result = await prisma.cartItem.deleteMany({
      where: {
        userId: userId,
        isOrdered: false
      }
    });

    success(res, { deletedCount: result.count }, 'Cart cleared successfully');
  } catch (err) {
    next(err);
  }
};

module.exports = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart
};