const { PrismaClient } = require('@prisma/client');
const { success, error } = require('../utils/response');

const prisma = new PrismaClient();

/**
 * Get current user profile
 */
const getUserProfile = async (req, res, next) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!user) {
      return error(res, 404, 'User not found');
    }

    success(res, user);
  } catch (err) {
    next(err);
  }
};

/**
 * Update current user profile
 */
const updateUserProfile = async (req, res, next) => {
  try {
    const { firstName, lastName } = req.body;

    const user = await prisma.user.update({
      where: { id: req.user.userId },
      data: {
        ...(firstName && { firstName }),
        ...(lastName && { lastName })
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        updatedAt: true
      }
    });

    success(res, user, 'Profile updated successfully');
  } catch (err) {
    next(err);
  }
};

/**
 * Get all users (Admin only)
 */
const getAllUsers = async (req, res, next) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        _count: {
          select: {
            cartItems: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    success(res, users);
  } catch (err) {
    next(err);
  }
};

/**
 * Get user by ID (Admin only)
 */
const getUserById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const user = await prisma.user.findUnique({
      where: { id: parseInt(id) },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        createdAt: true,
        updatedAt: true,
        cartItems: {
          select: {
            id: true,
            quantity: true,
            product: {
              select: {
                id: true,
                itemName: true,
                price: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      return error(res, 404, 'User not found');
    }

    success(res, user);
  } catch (err) {
    next(err);
  }
};

/**
 * Update user (Admin only)
 */
const updateUser = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { firstName, lastName, email } = req.body;

    const user = await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        ...(firstName && { firstName }),
        ...(lastName && { lastName }),
        ...(email && { email })
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        updatedAt: true
      }
    });

    success(res, user, 'User updated successfully');
  } catch (err) {
    next(err);
  }
};

/**
 * Delete user (Admin only)
 */
const deleteUser = async (req, res, next) => {
  try {
    const { id } = req.params;

    await prisma.user.delete({
      where: { id: parseInt(id) }
    });

    success(res, null, 'User deleted successfully');
  } catch (err) {
    next(err);
  }
};

module.exports = {
  getUserProfile,
  updateUserProfile,
  getAllUsers,
  getUserById,
  updateUser,
  deleteUser
};