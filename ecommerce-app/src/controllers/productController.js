const { PrismaClient } = require('@prisma/client');
const { success, error, paginated } = require('../utils/response');

const prisma = new PrismaClient();

/**
 * Create a new product (Admin only)
 */
const createProduct = async (req, res, next) => {
  try {
    const { itemName, tags, price, description, category, stock } = req.body;

    if (!itemName || !price) {
      return error(res, 400, 'Item name and price are required');
    }

    if (price < 0) {
      return error(res, 400, 'Price must be a positive number');
    }

    const product = await prisma.product.create({
      data: {
        itemName,
        tags: tags || [],
        price: parseInt(price),
        description,
        category,
        stock: stock || 0
      },
      select: {
        id: true,
        itemName: true,
        price: true,
        tags: true,
        description: true,
        category: true,
        stock: true,
        createdAt: true
      }
    });

    success(res, product, 'Product created successfully', 201);
  } catch (err) {
    next(err);
  }
};

/**
 * Get all products with pagination and filtering
 */
const getAllProducts = async (req, res, next) => {
  try {
    const {
      tag,
      minPrice,
      maxPrice,
      category,
      page = 1,
      limit = 20
    } = req.query;

    const where = {};

    if (tag) {
      where.tags = {
        has: tag
      };
    }

    if (category) {
      where.category = {
        contains: category,
        mode: 'insensitive'
      };
    }

    if (minPrice || maxPrice) {
      where.price = {};
      if (minPrice) where.price.gte = parseInt(minPrice);
      if (maxPrice) where.price.lte = parseInt(maxPrice);
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        select: {
          id: true,
          itemName: true,
          price: true,
          tags: true,
          category: true,
          stock: true,
          createdAt: true
        },
        skip,
        take: parseInt(limit),
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.product.count({ where })
    ]);

    paginated(res, products, {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get product by ID
 */
const getProductById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const product = await prisma.product.findUnique({
      where: { id: parseInt(id) },
      select: {
        id: true,
        itemName: true,
        price: true,
        tags: true,
        description: true,
        category: true,
        stock: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!product) {
      return error(res, 404, 'Product not found');
    }

    success(res, product);
  } catch (err) {
    next(err);
  }
};

/**
 * Update product (Admin only)
 */
const updateProduct = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { itemName, tags, price, description, category, stock } = req.body;

    const updateData = {};
    if (itemName) updateData.itemName = itemName;
    if (tags !== undefined) updateData.tags = tags;
    if (description !== undefined) updateData.description = description;
    if (category !== undefined) updateData.category = category;
    if (stock !== undefined) updateData.stock = parseInt(stock);

    if (price !== undefined) {
      if (price < 0) {
        return error(res, 400, 'Price must be a positive number');
      }
      updateData.price = parseInt(price);
    }

    const product = await prisma.product.update({
      where: { id: parseInt(id) },
      data: updateData,
      select: {
        id: true,
        itemName: true,
        price: true,
        tags: true,
        description: true,
        category: true,
        stock: true,
        updatedAt: true
      }
    });

    success(res, product, 'Product updated successfully');
  } catch (err) {
    next(err);
  }
};

/**
 * Delete product (Admin only)
 */
const deleteProduct = async (req, res, next) => {
  try {
    const { id } = req.params;

    await prisma.product.delete({
      where: { id: parseInt(id) }
    });

    success(res, null, 'Product deleted successfully');
  } catch (err) {
    next(err);
  }
};

/**
 * Search products by name, tags, or category
 */
const searchProducts = async (req, res, next) => {
  try {
    const { q, page = 1, limit = 20 } = req.query;

    if (!q) {
      return error(res, 400, 'Search query is required');
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {
      OR: [
        {
          itemName: {
            contains: q,
            mode: 'insensitive'
          }
        },
        {
          description: {
            contains: q,
            mode: 'insensitive'
          }
        },
        {
          category: {
            contains: q,
            mode: 'insensitive'
          }
        },
        {
          tags: {
            hasSome: [q]
          }
        }
      ]
    };

    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        select: {
          id: true,
          itemName: true,
          price: true,
          tags: true,
          category: true,
          stock: true
        },
        skip,
        take: parseInt(limit),
        orderBy: {
          itemName: 'asc'
        }
      }),
      prisma.product.count({ where })
    ]);

    paginated(res, products, {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get products by category
 */
const getProductsByCategory = async (req, res, next) => {
  try {
    const { category } = req.params;
    const { page = 1, limit = 20 } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {
      category: {
        equals: category,
        mode: 'insensitive'
      }
    };

    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        select: {
          id: true,
          itemName: true,
          price: true,
          tags: true,
          category: true,
          stock: true
        },
        skip,
        take: parseInt(limit),
        orderBy: {
          itemName: 'asc'
        }
      }),
      prisma.product.count({ where })
    ]);

    paginated(res, products, {
      page: parseInt(page),
      limit: parseInt(limit),
      total
    });
  } catch (err) {
    next(err);
  }
};

module.exports = {
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  searchProducts,
  getProductsByCategory
};