const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Create a new product
exports.createProduct = async (req, res) => {
  try {
    const { itemName, tags, price } = req.body;
    
    if (!itemName || !price) {
      return res.status(400).json({ 
        error: 'Item name and price are required' 
      });
    }

    if (price < 0) {
      return res.status(400).json({ 
        error: 'Price must be a positive number' 
      });
    }

    const product = await prisma.product.create({
      data: { 
        itemName, 
        tags: tags || [], 
        price: parseInt(price)
      },
    });
    
    res.status(201).json(product);
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
};

// Get all products
exports.getAllProducts = async (req, res) => {
  try {
    const { tag, minPrice, maxPrice } = req.query;
    
    const where = {};
    
    if (tag) {
      where.tags = {
        has: tag
      };
    }
    
    if (minPrice || maxPrice) {
      where.price = {};
      if (minPrice) where.price.gte = parseInt(minPrice);
      if (maxPrice) where.price.lte = parseInt(maxPrice);
    }

    const products = await prisma.product.findMany({
      where,
      include: {
        cartItems: {
          include: {
            user: true
          }
        }
      }
    });
    
    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
};

// Get product by ID
exports.getProductById = async (req, res) => {
  try {
    const { id } = req.params;
    const product = await prisma.product.findUnique({
      where: { id: parseInt(id) },
      include: {
        cartItems: {
          include: {
            user: true
          }
        }
      }
    });
    
    if (!product) {
      return res.status(404).json({ error: 'Product not found' });
    }
    
    res.json(product);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: 'Failed to fetch product' });
  }
};

// Update product
exports.updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const { itemName, tags, price } = req.body;
    
    const updateData = {};
    if (itemName) updateData.itemName = itemName;
    if (tags !== undefined) updateData.tags = tags;
    if (price !== undefined) {
      if (price < 0) {
        return res.status(400).json({ 
          error: 'Price must be a positive number' 
        });
      }
      updateData.price = parseInt(price);
    }
    
    const product = await prisma.product.update({
      where: { id: parseInt(id) },
      data: updateData,
    });
    
    res.json(product);
  } catch (error) {
    console.error('Error updating product:', error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Product not found' });
    }
    res.status(500).json({ error: 'Failed to update product' });
  }
};

// Delete product
exports.deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;
    
    await prisma.product.delete({
      where: { id: parseInt(id) }
    });
    
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting product:', error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Product not found' });
    }
    res.status(500).json({ error: 'Failed to delete product' });
  }
};

// Search products by name or tags
exports.searchProducts = async (req, res) => {
  try {
    const { q } = req.query;
    
    if (!q) {
      return res.status(400).json({ error: 'Search query is required' });
    }
    
    const products = await prisma.product.findMany({
      where: {
        OR: [
          {
            itemName: {
              contains: q,
              mode: 'insensitive'
            }
          },
          {
            tags: {
              hasSome: [q]
            }
          }
        ]
      }
    });
    
    res.json(products);
  } catch (error) {
    console.error('Error searching products:', error);
    res.status(500).json({ error: 'Failed to search products' });
  }
};