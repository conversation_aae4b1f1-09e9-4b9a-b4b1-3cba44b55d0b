#!/bin/bash

# E-commerce API Database Setup Script
# This script helps you set up PostgreSQL and initialize your database

echo "🚀 E-commerce API Database Setup"
echo "================================="

# Check if Docker is installed
if command -v docker &> /dev/null; then
    echo "✅ Docker is installed"
    DOCKER_AVAILABLE=true
else
    echo "❌ Docker is not installed"
    DOCKER_AVAILABLE=false
fi

# Check if PostgreSQL is installed locally
if command -v psql &> /dev/null; then
    echo "✅ PostgreSQL is installed locally"
    POSTGRES_LOCAL=true
else
    echo "❌ PostgreSQL is not installed locally"
    POSTGRES_LOCAL=false
fi

echo ""
echo "Choose your database setup option:"
echo "1) Use Docker Compose (Recommended)"
echo "2) Use Local PostgreSQL"
echo "3) Exit"

read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        if [ "$DOCKER_AVAILABLE" = true ]; then
            echo ""
            echo "🐳 Setting up PostgreSQL with Docker Compose..."
            
            # Start PostgreSQL container
            docker-compose up -d db
            
            echo "⏳ Waiting for PostgreSQL to be ready..."
            sleep 10
            
            # Check if container is running
            if docker-compose ps db | grep -q "Up"; then
                echo "✅ PostgreSQL container is running"
                
                # Initialize Prisma
                echo "🔧 Initializing Prisma schema..."
                npm run db:generate
                npm run db:push
                
                # Seed the database
                echo "🌱 Seeding database with sample data..."
                npm run db:seed
                
                echo ""
                echo "🎉 Database setup complete!"
                echo "📊 Your PostgreSQL database is running on localhost:5432"
                echo "🔗 Connection string: postgresql://postgres:postgres@localhost:5432/ecommerce"
                
            else
                echo "❌ Failed to start PostgreSQL container"
                exit 1
            fi
        else
            echo "❌ Docker is not available. Please install Docker first."
            exit 1
        fi
        ;;
    2)
        if [ "$POSTGRES_LOCAL" = true ]; then
            echo ""
            echo "🗄️ Setting up with Local PostgreSQL..."
            
            # Create database
            echo "📝 Creating database 'ecommerce'..."
            createdb ecommerce 2>/dev/null || echo "Database 'ecommerce' may already exist"
            
            # Initialize Prisma
            echo "🔧 Initializing Prisma schema..."
            npm run db:generate
            npm run db:push
            
            # Seed the database
            echo "🌱 Seeding database with sample data..."
            npm run db:seed
            
            echo ""
            echo "🎉 Database setup complete!"
            echo "📊 Your local PostgreSQL database is ready"
            
        else
            echo "❌ PostgreSQL is not installed locally."
            echo "Please install PostgreSQL first or choose Docker option."
            exit 1
        fi
        ;;
    3)
        echo "👋 Exiting setup"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

echo ""
echo "🔑 Demo Login Credentials:"
echo "   Email: <EMAIL>"
echo "   Email: <EMAIL>" 
echo "   Email: <EMAIL>"
echo "   Email: <EMAIL>"
echo "   Password: password123 (for all users)"
echo ""
echo "🚀 You can now start the API server with:"
echo "   npm run dev"
echo ""
echo "📚 API Documentation available at:"
echo "   docs/api.md"
