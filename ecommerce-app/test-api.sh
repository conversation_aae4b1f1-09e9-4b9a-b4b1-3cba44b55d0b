#!/bin/bash

# E-commerce API Test Script
# This script tests all major API endpoints

echo "🧪 E-commerce API Test Suite"
echo "============================"

BASE_URL="http://localhost:3000"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test function
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local headers=$4
    local description=$5
    
    echo -e "\n${BLUE}Testing: $description${NC}"
    echo "Endpoint: $method $endpoint"
    
    if [ -n "$data" ]; then
        if [ -n "$headers" ]; then
            response=$(curl -s -X $method "$BASE_URL$endpoint" -H "Content-Type: application/json" -H "$headers" -d "$data")
        else
            response=$(curl -s -X $method "$BASE_URL$endpoint" -H "Content-Type: application/json" -d "$data")
        fi
    else
        if [ -n "$headers" ]; then
            response=$(curl -s -X $method "$BASE_URL$endpoint" -H "$headers")
        else
            response=$(curl -s -X $method "$BASE_URL$endpoint")
        fi
    fi
    
    if echo "$response" | jq -e '.success' > /dev/null 2>&1; then
        echo -e "${GREEN}✅ PASS${NC}"
        echo "$response" | jq .
    else
        echo -e "${RED}❌ FAIL${NC}"
        echo "$response"
    fi
}

# 1. Health Check
test_endpoint "GET" "/health" "" "" "Health Check"

# 2. API Info
test_endpoint "GET" "/api/v1" "" "" "API Info"

# 3. Get Products
test_endpoint "GET" "/api/v1/products" "" "" "Get Products (Public)"

# 4. Search Products
test_endpoint "GET" "/api/v1/products/search?q=apple" "" "" "Search Products"

# 5. User Login
echo -e "\n${BLUE}Testing: User Authentication${NC}"
login_response=$(curl -s -X POST "$BASE_URL/api/v1/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"password123"}')

if echo "$login_response" | jq -e '.success' > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Login PASS${NC}"
    ACCESS_TOKEN=$(echo "$login_response" | jq -r '.data.accessToken')
    echo "Access token obtained"
else
    echo -e "${RED}❌ Login FAIL${NC}"
    echo "$login_response"
    exit 1
fi

# 6. Get User Profile (Protected)
test_endpoint "GET" "/api/v1/users/profile" "" "Authorization: Bearer $ACCESS_TOKEN" "Get User Profile (Protected)"

# 7. Get User Cart (Protected)
test_endpoint "GET" "/api/v1/cart" "" "Authorization: Bearer $ACCESS_TOKEN" "Get User Cart (Protected)"

# 8. Add to Cart (Protected)
test_endpoint "POST" "/api/v1/cart" '{"productId":2,"quantity":1}' "Authorization: Bearer $ACCESS_TOKEN" "Add to Cart (Protected)"

# 9. Test Rate Limiting (Optional)
echo -e "\n${BLUE}Testing: Rate Limiting${NC}"
echo "Making 3 rapid requests to test rate limiting..."
for i in {1..3}; do
    curl -s "$BASE_URL/api/v1/products" > /dev/null
    echo "Request $i sent"
done
echo -e "${GREEN}✅ Rate limiting test completed${NC}"

echo -e "\n🎉 ${GREEN}API Test Suite Completed!${NC}"
echo -e "\n📚 For complete API documentation, see: docs/api.md"
echo -e "🔗 API Base URL: $BASE_URL/api/v1"
