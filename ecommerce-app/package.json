{"name": "ecommerce-app", "version": "1.0.0", "description": "Simple Node.js e-commerce app with Express and Prisma", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "node prisma/seed.js"}, "dependencies": {"express": "^4.18.2", "@prisma/client": "^5.7.0", "dotenv": "^16.3.1"}, "devDependencies": {"prisma": "^5.7.0", "nodemon": "^3.0.2"}, "keywords": ["ecommerce", "nodejs", "express", "prisma", "postgresql"], "author": "", "license": "MIT"}