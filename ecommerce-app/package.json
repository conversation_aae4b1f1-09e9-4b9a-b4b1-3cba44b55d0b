{"name": "ecommerce-app", "version": "1.0.0", "description": "Simple Node.js e-commerce app with Express and Prisma", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "node prisma/seed.js"}, "dependencies": {"@prisma/client": "^5.7.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.0.2", "prisma": "^5.7.0"}, "keywords": ["ecommerce", "nodejs", "express", "prisma", "postgresql"], "author": "", "license": "MIT"}